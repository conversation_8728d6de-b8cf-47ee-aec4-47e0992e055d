<?php
/**
 * Test script to verify XSS protection middleware is working
 * 
 * This script can be run to test if the XSS middleware is properly
 * sanitizing input and adding security headers.
 * 
 * Usage: php test-xss-protection.php
 */

echo "=== XSS Protection Middleware Test ===\n\n";

// Test 1: Check if XSS middleware is registered in Kernel.php
echo "1. Checking middleware registration...\n";

$kernelPath = __DIR__ . '/app/Http/Kernel.php';
if (file_exists($kernelPath)) {
    $kernelContent = file_get_contents($kernelPath);
    
    // Check if XSS middleware is in web group
    if (strpos($kernelContent, '\App\Http\Middleware\XSS::class,') !== false) {
        echo "   ✓ XSS middleware found in Kernel.php\n";
        
        // Check if it's in web middleware group
        if (preg_match("/('web'\s*=>\s*\[.*?\\\App\\\Http\\\Middleware\\\XSS::class)/s", $kernelContent)) {
            echo "   ✓ XSS middleware is registered in 'web' middleware group\n";
        } else {
            echo "   ⚠ XSS middleware found but may not be in 'web' group\n";
        }
        
        // Check if it's in api middleware group
        if (preg_match("/('api'\s*=>\s*\[.*?\\\App\\\Http\\\Middleware\\\XSS::class)/s", $kernelContent)) {
            echo "   ✓ XSS middleware is registered in 'api' middleware group\n";
        } else {
            echo "   ⚠ XSS middleware not found in 'api' group\n";
        }
        
        // Check if alias exists
        if (strpos($kernelContent, "'XSS' => \\App\\Http\\Middleware\\XSS::class") !== false) {
            echo "   ✓ XSS middleware alias is registered\n";
        }
    } else {
        echo "   ✗ XSS middleware not found in Kernel.php\n";
    }
} else {
    echo "   ✗ Kernel.php not found\n";
}

// Test 2: Check if XSS middleware file exists and has proper structure
echo "\n2. Checking XSS middleware file...\n";

$xssPath = __DIR__ . '/app/Http/Middleware/XSS.php';
if (file_exists($xssPath)) {
    echo "   ✓ XSS middleware file exists\n";
    
    $xssContent = file_get_contents($xssPath);
    
    // Check for key security features
    if (strpos($xssContent, 'FILTER_SANITIZE_SPECIAL_CHARS') !== false) {
        echo "   ✓ Input sanitization implemented\n";
    }
    
    if (strpos($xssContent, 'X-XSS-Protection') !== false) {
        echo "   ✓ X-XSS-Protection header implemented\n";
    }
    
    if (strpos($xssContent, 'X-Content-Type-Options') !== false) {
        echo "   ✓ X-Content-Type-Options header implemented\n";
    }
    
    if (strpos($xssContent, 'X-Frame-Options') !== false) {
        echo "   ✓ X-Frame-Options header implemented\n";
    }
    
    if (strpos($xssContent, 'preg_replace') !== false) {
        echo "   ✓ Script tag removal implemented\n";
    }
} else {
    echo "   ✗ XSS middleware file not found\n";
}

// Test 3: Check route configuration
echo "\n3. Checking route configuration...\n";

$webRoutesPath = __DIR__ . '/routes/web.php';
if (file_exists($webRoutesPath)) {
    echo "   ✓ Web routes file exists\n";
    
    $routesContent = file_get_contents($webRoutesPath);
    
    // Check if any routes are explicitly using XSS middleware
    if (strpos($routesContent, "'XSS'") !== false) {
        echo "   ✓ Some routes explicitly use XSS middleware\n";
    } else {
        echo "   ℹ No explicit XSS middleware usage found (will use from middleware group)\n";
    }
} else {
    echo "   ✗ Web routes file not found\n";
}

echo "\n=== Test Summary ===\n";
echo "The XSS protection middleware should now be active on all web and API routes.\n";
echo "It will:\n";
echo "- Sanitize input data to prevent XSS attacks\n";
echo "- Remove script tags and JavaScript event handlers\n";
echo "- Add security headers (X-XSS-Protection, X-Content-Type-Options, X-Frame-Options)\n";
echo "- Skip certain routes that need to allow HTML content\n\n";

echo "To test in browser:\n";
echo "1. Try submitting a form with <script>alert('xss')</script>\n";
echo "2. Check browser developer tools for security headers\n";
echo "3. Verify that malicious scripts are sanitized\n\n";

echo "Test completed!\n";
