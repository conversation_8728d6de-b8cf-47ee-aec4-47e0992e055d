<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="container-fluid py-4">
        <div class="row">
            <!-- Left Sidebar - Course Navigation -->
            <div class="col-lg-3">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white py-3">
                        <h5 class="mb-0"><?php echo e($course->name); ?></h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <div class="accordion" id="courseContentAccordion">
                                <div class="accordion-item border-0">
                                    <h2 class="accordion-header" id="headingLectures">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseLectures" aria-expanded="true" aria-controls="collapseLectures">
                                            <i class="fas fa-graduation-cap me-2"></i> Course Content
                                        </button>
                                    </h2>
                                    <div id="collapseLectures" class="accordion-collapse collapse show" aria-labelledby="headingLectures">
                                        <div class="accordion-body p-0">
                                            <div class="list-group list-group-flush">
                                                <?php $__currentLoopData = $lectures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lecItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <!-- Lecture Video -->
                                                    <a href="<?php echo e(route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lecItem->id])); ?>"
                                                        class="list-group-item list-group-item-action d-flex align-items-center ps-4 <?php echo e($lecture->id == $lecItem->id && !$isViewingPdf ? 'active' : ''); ?>">
                                                        <i class="fas fa-play-circle me-2"></i>
                                                        <div class="flex-grow-1">
                                                            <span><?php echo e($lecItem->name); ?></span>
                                                            <small class="d-block text-muted"><?php echo e($lecItem->duration ?? 'N/A'); ?></small>
                                                        </div>
                                                    </a>

                                                    <!-- Lecture PDF (indented) -->
                                                    <?php if($lecItem->pdf_file_path): ?>
                                                    <a href="<?php echo e(route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lecItem->id])); ?>?view=pdf"
                                                        class="list-group-item list-group-item-action d-flex align-items-center ps-5 <?php echo e($lecture->id == $lecItem->id && $isViewingPdf ? 'active' : ''); ?>">
                                                        <i class="fas fa-file-pdf text-danger me-2"></i>
                                                        <div>
                                                            <span><?php echo e($lecItem->name); ?> PDF</span>
                                                        </div>
                                                    </a>
                                                    <?php endif; ?>

                                                    <!-- Lecture Quizzes (indented) -->
                                                    <?php
                                                        $quizzes = $lecItem->quizzes;
                                                        $userId = auth()->id();
                                                    ?>
                                                    <?php if($quizzes && $quizzes->count() > 0): ?>
                                                        <?php $__currentLoopData = $quizzes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $quiz): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <a href="<?php echo e(route('quiz.show', ['course' => $course->id, 'lecture' => $lecItem->id, 'quiz' => $quiz->id])); ?>"
                                                                class="list-group-item list-group-item-action d-flex align-items-center ps-5">
                                                                <?php if($quiz->isPassed($userId)): ?>
                                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                                <?php else: ?>
                                                                    <i class="fas fa-question-circle text-warning me-2"></i>
                                                                <?php endif; ?>
                                                                <div>
                                                                    <span>Quiz: <?php echo e($quiz->title); ?></span>
                                                                    <?php if($quiz->isPassed($userId)): ?>
                                                                        <small class="d-block text-success">Passed</small>
                                                                    <?php elseif($quiz->hasInProgressAttempt($userId)): ?>
                                                                        <small class="d-block text-primary">In Progress</small>
                                                                    <?php else: ?>
                                                                        <small class="d-block text-muted"><?php echo e($quiz->total_points); ?> points</small>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </a>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-lg-9">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('user.dashboard')); ?>">My Courses</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('user.course.purchased', $course->id)); ?>"><?php echo e($course->name); ?></a></li>
                        <li class="breadcrumb-item active"><?php echo e($lecture->name); ?></li>
                    </ol>
                </nav>

                <!-- Lecture Player -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <h3 class="mb-3"><?php echo e($lecture->name); ?></h3>

                        <?php
                            $viewMode = request()->query('view', 'video');
                            $userProgress = $lecture->getProgressForUser(Auth::id());
                            $progressPercent = $userProgress ? $userProgress->progress_percent : 0;
                            $isCompleted = $userProgress ? $userProgress->completed : false;
                        ?>

                        <!-- Lecture Progress Bar -->
                        <div class="lecture-progress mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="small text-muted">Your progress</span>
                                <span class="small fw-bold lecture-progress-display"><?php echo e(number_format($progressPercent, 0)); ?>%</span>
                            </div>
                            <div class="progress lecture-progress-height">
                                <div class="progress-bar lecture-progress-bar <?php echo e($isCompleted ? 'bg-success' : 'bg-primary'); ?>"
                                     role="progressbar"
                                     data-width="<?php echo e($progressPercent); ?>"
                                     aria-valuenow="<?php echo e($progressPercent); ?>"
                                     aria-valuemin="0"
                                     aria-valuemax="100"></div>
                            </div>
                        </div>

                        <?php if($viewMode == 'pdf' && $lecture->pdf_file_path): ?>
                            <!-- PDF Viewer -->
                            <div class="pdf-viewer-height mb-4">
                                <object data="<?php echo e(asset('storage/' . $lecture->pdf_file_path)); ?>" type="application/pdf" width="100%" height="100%">
                                    <div class="p-4 bg-light rounded text-center">
                                        <p class="mb-2">
                                            <i class="fas fa-exclamation-circle text-warning fa-2x mb-3"></i><br>
                                            Your browser doesn't support embedded PDF viewing.
                                        </p>
                                        <a href="<?php echo e(asset('storage/' . $lecture->pdf_file_path)); ?>" class="btn btn-primary mb-2" target="_blank">
                                            <i class="fas fa-external-link-alt me-1"></i> Open PDF in New Tab
                                        </a>
                                        <a href="<?php echo e(asset('storage/' . $lecture->pdf_file_path)); ?>" class="btn btn-outline-primary" download>
                                            <i class="fas fa-download me-1"></i> Download PDF
                                        </a>
                                    </div>
                                </object>
                            </div>

                            <!-- PDF Actions -->
                            <div class="d-flex justify-content-between mb-4">
                                <a href="<?php echo e(route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lecture->id])); ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-video me-1"></i> Switch to Video
                                </a>
                            </div>
                        <?php else: ?>
                            <!-- Video Player -->
                            <div class="ratio ratio-16x9 mb-4 video-container">
                                <?php if($lecture->youtube_url): ?>
                                    <?php
                                        $youtubeId = null;
                                        if (preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $lecture->youtube_url, $matches)) {
                                            $youtubeId = $matches[1];
                                        }
                                    ?>
                                    <?php if($youtubeId): ?>
                                        <div class="video-player" data-plyr-provider="youtube" data-plyr-embed-id="<?php echo e($youtubeId); ?>"></div>
                                    <?php else: ?>
                                        <div class="d-flex align-items-center justify-content-center bg-dark text-white">
                                            <div class="text-center">
                                                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                                <h5>Invalid YouTube URL</h5>
                                                <p>The video URL format is not supported.</p>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <div class="d-flex align-items-center justify-content-center bg-dark text-white">
                                        <div class="text-center">
                                            <i class="fas fa-video fa-3x mb-3"></i>
                                            <h5>No Video Available</h5>
                                            <p>This lecture doesn't have a video yet.</p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Video Actions -->
                            <div class="d-flex justify-content-between mb-4">
                                <?php if($lecture->pdf_file_path): ?>
                                    <a href="<?php echo e(route('user.lecture.purchased', ['course' => $course->id, 'lecture' => $lecture->id])); ?>?view=pdf" class="btn btn-outline-primary">
                                        <i class="fas fa-file-pdf me-1"></i> Switch to PDF
                                    </a>
                                <?php else: ?>
                                    <div></div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <!-- Lecture Description -->
                        <?php if($lecture->description): ?>
                            <div class="lecture-description mb-4">
                                <h5>Description</h5>
                                <div class="bg-light p-3 rounded">
                                    <?php echo nl2br(e($lecture->description)); ?>

                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Lecture Attachments -->
                        <?php if($lecture->attachments && $lecture->attachments->count() > 0): ?>
                            <div class="lecture-attachments mb-4">
                                <h5>Attachments</h5>
                                <div class="row">
                                    <?php $__currentLoopData = $lecture->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="card attachment-card">
                                                <div class="card-body text-center">
                                                    <?php
                                                        $extension = pathinfo($attachment->file_path, PATHINFO_EXTENSION);
                                                        $isImage = in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif', 'webp']);
                                                    ?>
                                                    
                                                    <?php if($isImage): ?>
                                                        <img src="<?php echo e(asset('storage/' . $attachment->file_path)); ?>" 
                                                             alt="<?php echo e($attachment->original_name); ?>" 
                                                             class="attachment-img-height mb-2 rounded">
                                                    <?php else: ?>
                                                        <i class="fas fa-file fa-3x text-primary mb-2"></i>
                                                    <?php endif; ?>
                                                    
                                                    <h6 class="card-title"><?php echo e($attachment->original_name); ?></h6>
                                                    <p class="card-text small text-muted"><?php echo e(strtoupper($extension)); ?> File</p>
                                                    <a href="<?php echo e(asset('storage/' . $attachment->file_path)); ?>" 
                                                       class="btn btn-sm btn-primary" 
                                                       download="<?php echo e($attachment->original_name); ?>">
                                                        <i class="fas fa-download me-1"></i> Download
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Lecture Rating Section -->
                        <div class="lecture-rating-section mt-5">
                            <h5>Rate This Lecture</h5>
                            
                            <!-- Average Rating Display -->
                            <div class="lecture-rating-stats mb-4 p-3 bg-light rounded">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center">
                                            <span class="h4 mb-0 me-3" id="average-rating"><?php echo e(number_format($lecture->averageRating(), 1)); ?></span>
                                            <div id="average-star-display">
                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                    <?php if($i <= $lecture->averageRating()): ?>
                                                        <i class="fas fa-star text-warning"></i>
                                                    <?php elseif($i - 0.5 <= $lecture->averageRating()): ?>
                                                        <i class="fas fa-star-half-alt text-warning"></i>
                                                    <?php else: ?>
                                                        <i class="far fa-star text-warning"></i>
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        <small class="text-muted">(<?php echo e($lecture->ratings()->count()); ?> ratings)</small>
                                    </div>
                                </div>
                            </div>

                            <!-- User Rating Form -->
                            <div class="user-rating-form mb-4">
                                <div class="mb-3">
                                    <label class="form-label">Your Rating:</label>
                                    <div class="rating-stars mb-2">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <i class="far fa-star rating-star" data-value="<?php echo e($i); ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <input type="hidden" id="rating-value" value="0">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="rating-comment" class="form-label">Comment (Optional):</label>
                                    <textarea class="form-control" id="rating-comment" rows="3" placeholder="Share your thoughts about this lecture..."></textarea>
                                </div>
                                
                                <button type="button" class="btn btn-primary" id="submit-rating">
                                    <i class="fas fa-star me-2"></i> Submit Rating
                                </button>
                            </div>

                            <!-- Ratings List -->
                            <div id="ratings-container">
                                <!-- Ratings will be loaded here via JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('styles'); ?>
        <link rel="stylesheet" href="https://cdn.plyr.io/3.6.8/plyr.css" />
        <link href="<?php echo e(asset('css/purchased-lecture-detail.css')); ?>" rel="stylesheet">
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
        <script src="https://cdn.plyr.io/3.6.8/plyr.js"></script>
        <script src="<?php echo e(asset('js/purchased-lecture-detail.js')); ?>"></script>
        <script nonce="<?php echo e(csp_nonce()); ?>">
            // Initialize recording warning flag
            let recordingWarningShown = false;
            let plyrPlayer = null;

            // Initialize Plyr player for YouTube videos
            function initializeVideoPlayer() {
                const videoPlayer = document.querySelector('.video-player');
                if (videoPlayer) {
                    const embedId = videoPlayer.getAttribute('data-plyr-embed-id');
                    
                    if (embedId) {
                        plyrPlayer = new Plyr(videoPlayer, {
                            controls: [
                                'play-large', 'play', 'progress', 'current-time', 'duration',
                                'mute', 'volume', 'settings', 'fullscreen'
                            ],
                            settings: ['quality', 'speed'],
                            youtube: {
                                noCookie: true,
                                rel: 0,
                                showinfo: 0,
                                iv_load_policy: 3,
                                modestbranding: 1,
                                origin: window.location.origin
                            },
                            ratio: '16:9',
                            keyboard: { focused: false, global: false }
                        });

                        plyrPlayer.on('ready', () => {
                            console.log('🎬 Plyr player ready');
                            addVideoSecurityMeasures();
                        });

                        plyrPlayer.on('ended', () => {
                            videoEndedRecently = true;
                            setTimeout(() => {
                                videoEndedRecently = false;
                            }, 30000);
                        });
                    }
                }
            }

            function addVideoSecurityMeasures() {
                const videoContainer = document.querySelector('.ratio-16x9');
                if (videoContainer) {
                    videoContainer.addEventListener('contextmenu', (e) => {
                        e.preventDefault();
                        return false;
                    });

                    videoContainer.addEventListener('selectstart', (e) => {
                        e.preventDefault();
                        return false;
                    });

                    videoContainer.addEventListener('dragstart', (e) => {
                        e.preventDefault();
                        return false;
                    });
                }
            }

            async function detectScreenRecording() {
                try {
                    if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
                        const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia;

                        navigator.mediaDevices.getDisplayMedia = function(...args) {
                            console.log('🚫 Screen recording attempt detected!');
                            showRecordingWarning();
                            return Promise.reject(new Error('Screen recording is not allowed on this page'));
                        };
                    }
                } catch (error) {
                    console.log('Screen recording detection setup failed:', error);
                }
            }

            let suspiciousActivityScore = 0;
            let lastActivityTime = Date.now();
            let videoEndedRecently = false;

            function monitorSuspiciousActivity() {
                setInterval(() => {
                    const now = Date.now();
                    const timeSinceLastActivity = now - lastActivityTime;

                    if (videoEndedRecently) {
                        console.log('🎬 Video ended recently, skipping suspicious activity detection');
                        return;
                    }

                    if (!document.hidden && timeSinceLastActivity > 60000) {
                        suspiciousActivityScore += 1;
                    }

                    if (timeSinceLastActivity < 10000) {
                        suspiciousActivityScore = Math.max(0, suspiciousActivityScore - 1);
                    }

                    if (suspiciousActivityScore > 8 && !recordingWarningShown && !videoEndedRecently) {
                        console.log('🚨 High suspicious activity score detected:', suspiciousActivityScore);
                        showRecordingWarning();
                        recordingWarningShown = true;
                    }

                    lastActivityTime = now;
                }, 15000);
            }

            ['mousemove', 'keydown', 'click'].forEach(event => {
                document.addEventListener(event, () => {
                    lastActivityTime = Date.now();
                });
            });

            function detectRecordingProcesses() {
                setInterval(() => {
                    if (performance.memory) {
                        const memoryUsage = performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize;

                        if (memoryUsage > 0.9 && !recordingWarningShown) {
                            suspiciousActivityScore += 2;
                        }
                    }
                }, 15000);
            }

            // Initialize all protection methods
            document.addEventListener('DOMContentLoaded', function() {
                initializeVideoPlayer();
                detectScreenRecording();
                monitorSuspiciousActivity();
                detectRecordingProcesses();

                console.log('🛡️ Advanced screen recording protection activated!');
                console.log('ℹ️ Alt+Tab and normal window switching will not trigger warnings');

                // Initialize progress bars with data attributes
                const progressBars = document.querySelectorAll('[data-width]');
                progressBars.forEach(bar => {
                    const width = bar.getAttribute('data-width');
                    bar.style.width = width + '%';
                });

                // Rating functionality
                const ratingStars = document.querySelectorAll('.rating-star');
                const ratingValue = document.getElementById('rating-value');
                const submitButton = document.getElementById('submit-rating');

                if (ratingStars.length > 0) {
                    loadRatings();

                    ratingStars.forEach(star => {
                        star.addEventListener('mouseover', function() {
                            const value = parseInt(this.dataset.value);
                            highlightStars(value);
                        });

                        star.addEventListener('mouseleave', function() {
                            const selectedValue = parseInt(ratingValue.value);
                            highlightStars(selectedValue);
                        });

                        star.addEventListener('click', function() {
                            const value = parseInt(this.dataset.value);
                            ratingValue.value = value;
                            highlightStars(value);
                        });
                    });
                }

                function highlightStars(count) {
                    ratingStars.forEach(star => {
                        const starValue = parseInt(star.dataset.value);
                        if (starValue <= count) {
                            star.classList.remove('far');
                            star.classList.add('fas', 'text-warning');
                        } else {
                            star.classList.remove('fas', 'text-warning');
                            star.classList.add('far');
                        }
                    });
                }

                if (submitButton) {
                    submitButton.addEventListener('click', function() {
                        const rating = parseInt(ratingValue.value);
                        const comment = document.getElementById('rating-comment').value.trim();

                        if (rating === 0) {
                            alert('Please select a rating before submitting.');
                            return;
                        }

                        submitButton.disabled = true;
                        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Submitting...';

                        const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                        fetch('<?php echo e(route('lectures.rate', ['lecture' => $lecture->id])); ?>', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': token,
                                'Accept': 'application/json'
                            },
                            body: JSON.stringify({
                                rating: rating,
                                comment: comment
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert(data.message);
                                document.getElementById('average-rating').textContent = parseFloat(data.average_rating).toFixed(1);
                                document.querySelector('.lecture-rating-stats .text-muted').textContent = `(${data.rating_count} ratings)`;
                                updateAverageStarDisplay(data.average_rating);
                                loadRatings();
                            } else {
                                alert('Error: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred while submitting your rating.');
                        })
                        .finally(() => {
                            submitButton.disabled = false;
                            submitButton.innerHTML = '<i class="fas fa-star me-2"></i> Submit Rating';
                        });
                    });
                }

                function updateAverageStarDisplay(rating) {
                    const stars = document.querySelectorAll('#average-star-display i');
                    stars.forEach((star, index) => {
                        star.className = '';
                        if (index + 1 <= rating) {
                            star.className = 'fas fa-star text-warning';
                        } else if (index + 0.5 <= rating) {
                            star.className = 'fas fa-star-half-alt text-warning';
                        }
                    });
                }

                function loadRatings() {
                    const ratingsContainer = document.getElementById('ratings-container');
                    
                    ratingsContainer.innerHTML = `
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Loading ratings...</p>
                        </div>
                    `;

                    fetch('<?php echo e(route('lectures.ratings', ['lecture' => $lecture->id])); ?>')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                if (data.ratings.length > 0) {
                                    let html = '<div class="ratings-list mt-4">';
                                    data.ratings.forEach(rating => {
                                        const date = new Date(rating.created_at);
                                        const formattedDate = date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });

                                        html += `
                                            <div class="rating-item mb-3 p-3 border-bottom">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <div>
                                                        <strong>${rating.user.name}</strong>
                                                        <div class="d-inline-block ms-2">`;

                                        for (let i = 1; i <= 5; i++) {
                                            if (i <= rating.rating) {
                                                html += '<i class="fas fa-star text-warning small"></i>';
                                            } else {
                                                html += '<i class="far fa-star text-warning small"></i>';
                                            }
                                        }

                                        html += `
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <small class="text-muted">${formattedDate}</small>
                                                    </div>
                                                </div>`;

                                        if (rating.comment) {
                                            html += `<p class="mb-0 text-muted">${rating.comment}</p>`;
                                        }

                                        html += `</div>`;
                                    });
                                    html += '</div>';
                                    ratingsContainer.innerHTML = html;
                                } else {
                                    ratingsContainer.innerHTML = `
                                        <div class="text-center py-3">
                                            <p class="text-muted">No ratings yet. Be the first to rate this lecture!</p>
                                        </div>
                                    `;
                                }
                            } else {
                                ratingsContainer.innerHTML = `
                                    <div class="alert alert-warning">
                                        Failed to load ratings.
                                    </div>
                                `;
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            ratingsContainer.innerHTML = `
                                <div class="alert alert-danger">
                                    An error occurred while loading ratings.
                                </div>
                            `;
                        });
                }
            });

            console.log('🔒 Comprehensive security system with screen recording protection loaded and active!');
        </script>
    <?php $__env->stopPush(); ?>

    
    <script>
// Method 4: Prevent specific recording shortcuts (improved)
document.addEventListener('keydown', function(e) {
    // Block Windows Game Bar (Win + G) - but allow other Win combinations
    if ((e.metaKey || e.key === 'Meta') && e.keyCode === 71) {
        e.preventDefault();
        showRecordingWarning();
        console.log('🚫 Windows Game Bar shortcut blocked');
        return false;
    }

    // Block specific recording shortcuts (but not Alt+Tab)
    if (e.altKey && e.keyCode === 82 && !e.ctrlKey && !e.shiftKey) { // Alt+R only
        e.preventDefault();
        showRecordingWarning();
        console.log('🚫 Alt+R recording shortcut blocked');
        return false;
    }

    // Block Print Screen
    if (e.keyCode === 44) { // Print Screen
        e.preventDefault();
        showSecurityWarning('Screenshots are disabled for content protection');
        console.log('🚫 Print Screen blocked');
        return false;
    }

    // Block Ctrl+Shift+R (some recording software)
    if (e.ctrlKey && e.shiftKey && e.keyCode === 82) {
        e.preventDefault();
        showRecordingWarning();
        console.log('🚫 Ctrl+Shift+R recording shortcut blocked');
        return false;
    }
});

// Screen recording warning function
function showRecordingWarning() {
    // Remove existing warning if any
    const existingWarning = document.getElementById('recording-warning');
    if (existingWarning) {
        existingWarning.remove();
    }

    // Create recording warning popup
    const warning = document.createElement('div');
    warning.id = 'recording-warning';
    warning.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #ff0000, #cc0000);
        color: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(255, 0, 0, 0.5);
        z-index: 20000;
        font-family: Arial, sans-serif;
        text-align: center;
        max-width: 400px;
        animation: slideIn 0.5s ease-out;
        border: 3px solid #ffffff;
    `;

    warning.innerHTML = `
        <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
        <h2 style="margin-bottom: 15px; color: white;">Content Protection Notice</h2>
        <p style="margin-bottom: 20px; font-size: 16px;">
            Our system detected unusual activity that may indicate screen recording.
        </p>
        <p style="margin-bottom: 25px; font-size: 14px; opacity: 0.9;">
            If you're not recording, this might be a false positive. Please refresh to continue watching.
        </p>
        <div style="margin-bottom: 20px;">
            <button onclick="location.reload()" style="
                background: white;
                color: #ff0000;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                cursor: pointer;
                font-size: 16px;
                transition: all 0.3s ease;
                margin-right: 10px;
            " onmouseover="this.style.background='#f0f0f0'" onmouseout="this.style.background='white'">
                🔄 Refresh Page
            </button>
            <button onclick="document.getElementById('recording-warning').remove(); document.querySelector('.ratio-16x9').style.filter='none'; document.querySelector('.ratio-16x9').style.pointerEvents='auto';" style="
                background: transparent;
                color: white;
                border: 2px solid white;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.3s ease;
            " onmouseover="this.style.background='rgba(255,255,255,0.1)'" onmouseout="this.style.background='transparent'">
                Continue Anyway
            </button>
        </div>
        <p style="font-size: 12px; opacity: 0.7; margin: 0;">
            Note: Recording copyrighted content may violate terms of service
        </p>
    `;

    document.body.appendChild(warning);

    // Also blur the video content
    const videoContainer = document.querySelector('.ratio-16x9');
    if (videoContainer) {
        videoContainer.style.filter = 'blur(20px)';
        videoContainer.style.pointerEvents = 'none';
    }

    // Auto-dismiss warning after 30 seconds if user doesn't interact
    setTimeout(() => {
        const warningElement = document.getElementById('recording-warning');
        if (warningElement) {
            console.log('⏰ Auto-dismissing recording warning after 30 seconds');
            warningElement.remove();
            if (videoContainer) {
                videoContainer.style.filter = 'none';
                videoContainer.style.pointerEvents = 'auto';
            }
            // Reset the warning flag so it can trigger again if needed
            recordingWarningShown = false;
        }
    }, 30000); // 30 seconds
}

// Method 5: Advanced recording detection via user behavior patterns (improved)
let suspiciousActivityScore = 0;
let lastActivityTime = Date.now();
let videoEndedRecently = false;

function monitorSuspiciousActivity() {
    // Monitor for patterns that indicate recording software
    setInterval(() => {
        const now = Date.now();
        const timeSinceLastActivity = now - lastActivityTime;

        // Don't trigger if video ended recently (user might be inactive after video ends)
        if (videoEndedRecently) {
            console.log('🎬 Video ended recently, skipping suspicious activity detection');
            return;
        }

        // If user is inactive for long periods but page is still focused (suspicious)
        // But only if it's been a while since video ended
        if (!document.hidden && timeSinceLastActivity > 60000) { // Increased from 30 seconds to 60 seconds
            suspiciousActivityScore += 1;
        }

        // Reset score if user is actively using the page
        if (timeSinceLastActivity < 10000) { // Increased from 5 seconds to 10 seconds
            suspiciousActivityScore = Math.max(0, suspiciousActivityScore - 1);
        }

        // Trigger warning only if score is very high and no recent video activity
        if (suspiciousActivityScore > 8 && !recordingWarningShown && !videoEndedRecently) { // Increased threshold from 5 to 8
            console.log('🚨 High suspicious activity score detected:', suspiciousActivityScore);
            showRecordingWarning();
            recordingWarningShown = true;
        }

        lastActivityTime = now;
    }, 15000); // Check every 15 seconds (increased from 10 seconds)
}

// Track user activity to reset suspicious activity
document.addEventListener('mousemove', () => {
    lastActivityTime = Date.now();
});

document.addEventListener('keydown', () => {
    lastActivityTime = Date.now();
});

document.addEventListener('click', () => {
    lastActivityTime = Date.now();
});

// Method 6: Detect recording software by checking for specific processes (limited browser capability)
function detectRecordingProcesses() {
    // This is a placeholder for more advanced detection
    // In a real implementation, you might use browser APIs to detect
    // performance changes that indicate recording software

    setInterval(() => {
        // Check for performance degradation that might indicate recording
        if (performance.memory) {
            const memoryUsage = performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize;

            // If memory usage is consistently high, might indicate recording
            if (memoryUsage > 0.9 && !recordingWarningShown) {
                suspiciousActivityScore += 2;
            }
        }
    }, 15000);
}

// Initialize all protection methods
document.addEventListener('DOMContentLoaded', function() {
    detectScreenRecording();
    monitorSuspiciousActivity();
    detectRecordingProcesses();

    console.log('🛡️ Advanced screen recording protection activated!');
    console.log('ℹ️ Alt+Tab and normal window switching will not trigger warnings');
});

console.log('🔒 Comprehensive security system with screen recording protection loaded and active!');
</script>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\laravel\iec-courses-app\resources\views/course/purchased-lecture-detail.blade.php ENDPATH**/ ?>